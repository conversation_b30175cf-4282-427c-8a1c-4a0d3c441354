[{"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "glob", "kind": 6, "isExtraImport": true, "importPath": "glob", "description": "glob", "detail": "glob", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "sysconfig", "kind": 6, "isExtraImport": true, "importPath": "sysconfig", "description": "sysconfig", "detail": "sysconfig", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "winreg", "kind": 6, "isExtraImport": true, "importPath": "winreg", "description": "winreg", "detail": "winreg", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Form", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Form", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Form", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "FastAPI", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Request", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "logger", "importPath": "loguru", "description": "loguru", "isExtraImport": true, "detail": "loguru", "documentation": {}}, {"label": "get_settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "get_settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "get_settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "get_settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "Settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "get_settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "get_settings", "importPath": "app.core.config", "description": "app.core.config", "isExtraImport": true, "detail": "app.core.config", "documentation": {}}, {"label": "ConversationService", "importPath": "app.services.rag.conversation_service", "description": "app.services.rag.conversation_service", "isExtraImport": true, "detail": "app.services.rag.conversation_service", "documentation": {}}, {"label": "ConversationService", "importPath": "app.services.rag.conversation_service", "description": "app.services.rag.conversation_service", "isExtraImport": true, "detail": "app.services.rag.conversation_service", "documentation": {}}, {"label": "ChatRequest", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatResponse", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatEngineType", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatRequest", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatResponse", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatEngineType", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexRequest", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexResponse", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "CollectionInfo", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "DocumentMetadata", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "DeleteCollectionResponse", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "CollectionInfo", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatRequest", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatResponse", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatEngineType", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "SourceInfo", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexRequest", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexResponse", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "CollectionInfo", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexRequest", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "DocumentMetadata", "importPath": "app.schemas.rag", "description": "app.schemas.rag", "isExtraImport": true, "detail": "app.schemas.rag", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Generator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "get_rag_config_manager", "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "isExtraImport": true, "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "RAGConfigManager", "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "isExtraImport": true, "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "get_rag_config_manager", "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "isExtraImport": true, "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "RAGConfigManager", "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "isExtraImport": true, "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "RAGConfigManager", "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "isExtraImport": true, "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "RAGConfigManager", "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "isExtraImport": true, "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "unquote", "importPath": "urllib.parse", "description": "urllib.parse", "isExtraImport": true, "detail": "urllib.parse", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "timezone", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "DocumentIndexingService", "importPath": "app.services.rag.document_indexing_service", "description": "app.services.rag.document_indexing_service", "isExtraImport": true, "detail": "app.services.rag.document_indexing_service", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "validator", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "validator", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "validator", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseSettings", "importPath": "pydantic_settings", "description": "pydantic_settings", "isExtraImport": true, "detail": "pydantic_settings", "documentation": {}}, {"label": "BaseSettings", "importPath": "pydantic_settings", "description": "pydantic_settings", "isExtraImport": true, "detail": "pydantic_settings", "documentation": {}}, {"label": "aiofiles", "kind": 6, "isExtraImport": true, "importPath": "aiofiles", "description": "aiofiles", "detail": "aiofiles", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "models", "importPath": "qdrant_client.http", "description": "qdrant_client.http", "isExtraImport": true, "detail": "qdrant_client.http", "documentation": {}}, {"label": "Distance", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "VectorParams", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "PointStruct", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "PointStruct", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "AsyncOpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "PromptTemplate", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "ChatSummaryMemoryBuffer", "importPath": "llama_index.core.memory", "description": "llama_index.core.memory", "isExtraImport": true, "detail": "llama_index.core.memory", "documentation": {}}, {"label": "SimpleChatEngine", "importPath": "llama_index.core.chat_engine", "description": "llama_index.core.chat_engine", "isExtraImport": true, "detail": "llama_index.core.chat_engine", "documentation": {}}, {"label": "Metadata<PERSON><PERSON>er", "importPath": "llama_index.core.vector_stores", "description": "llama_index.core.vector_stores", "isExtraImport": true, "detail": "llama_index.core.vector_stores", "documentation": {}}, {"label": "MetadataFilters", "importPath": "llama_index.core.vector_stores", "description": "llama_index.core.vector_stores", "isExtraImport": true, "detail": "llama_index.core.vector_stores", "documentation": {}}, {"label": "FilterOperator", "importPath": "llama_index.core.vector_stores", "description": "llama_index.core.vector_stores", "isExtraImport": true, "detail": "llama_index.core.vector_stores", "documentation": {}}, {"label": "RedisChatStore", "importPath": "llama_index.storage.chat_store.redis", "description": "llama_index.storage.chat_store.redis", "isExtraImport": true, "detail": "llama_index.storage.chat_store.redis", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "QdrantRepository", "importPath": "app.repositories.rag_repository", "description": "app.repositories.rag_repository", "isExtraImport": true, "detail": "app.repositories.rag_repository", "documentation": {}}, {"label": "QdrantRepository", "importPath": "app.repositories.rag_repository", "description": "app.repositories.rag_repository", "isExtraImport": true, "detail": "app.repositories.rag_repository", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "SentenceSplitter", "importPath": "llama_index.core.node_parser", "description": "llama_index.core.node_parser", "isExtraImport": true, "detail": "llama_index.core.node_parser", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "aiofiles.os", "kind": 6, "isExtraImport": true, "importPath": "aiofiles.os", "description": "aiofiles.os", "detail": "aiofiles.os", "documentation": {}}, {"label": "random", "kind": 6, "isExtraImport": true, "importPath": "random", "description": "random", "detail": "random", "documentation": {}}, {"label": "string", "kind": 6, "isExtraImport": true, "importPath": "string", "description": "string", "detail": "string", "documentation": {}}, {"label": "asynccontextmanager", "importPath": "contextlib", "description": "contextlib", "isExtraImport": true, "detail": "contextlib", "documentation": {}}, {"label": "asynccontextmanager", "importPath": "contextlib", "description": "contextlib", "isExtraImport": true, "detail": "contextlib", "documentation": {}}, {"label": "CORSMiddleware", "importPath": "fastapi.middleware.cors", "description": "fastapi.middleware.cors", "isExtraImport": true, "detail": "fastapi.middleware.cors", "documentation": {}}, {"label": "RequestValidationError", "importPath": "fastapi.exceptions", "description": "fastapi.exceptions", "isExtraImport": true, "detail": "fastapi.exceptions", "documentation": {}}, {"label": "HTTPException", "importPath": "starlette.exceptions", "description": "starlette.exceptions", "isExtraImport": true, "detail": "starlette.exceptions", "documentation": {}}, {"label": "RAGService", "importPath": "app.services.rag_service", "description": "app.services.rag_service", "isExtraImport": true, "detail": "app.services.rag_service", "documentation": {}}, {"label": "<PERSON><PERSON>", "kind": 6, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "class Tee:\n    def __init__(self, file):\n        self.f = file\n    def write(self, what):\n        if self.f is not None:\n            try:\n                self.f.write(what.replace(\"\\n\", \"\\r\\n\"))\n            except OSError:\n                pass\n        tee_f.write(what)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_root_hkey", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU\n        return winreg.HKEY_CURRENT_USER", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "create_shortcut", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def create_shortcut(\n    path, description, filename, arguments=\"\", workdir=\"\", iconpath=\"\", iconindex=0\n):\n    import pythoncom\n    from win32com.shell import shell\n    ilink = pythoncom.CoCreateInstance(\n        shell.CLSID_ShellLink,\n        None,\n        pythoncom.CLSCTX_INPROC_SERVER,\n        shell.IID_IShellLink,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_special_folder_path", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_special_folder_path(path_name):\n    from win32com.shell import shell, shellcon\n    for maybe in \"\"\"\n        CSIDL_COMMON_STARTMENU CSIDL_STARTMENU CSIDL_COMMON_APPDATA\n        CSIDL_LOCAL_APPDATA CSIDL_APPDATA CSIDL_COMMON_DESKTOPDIRECTORY\n        CSIDL_DESKTOPDIRECTORY CSIDL_COMMON_STARTUP CSIDL_STARTUP\n        CSIDL_COMMON_PROGRAMS CSIDL_PROGRAMS CSIDL_PROGRAM_FILES_COMMON\n        CSIDL_PROGRAM_FILES CSIDL_FONTS\"\"\".split():\n        if maybe == path_name:\n            csidl = getattr(shellcon, maybe)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "CopyTo", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def CopyTo(desc, src, dest):\n    import win32api\n    import win32con\n    while 1:\n        try:\n            win32api.CopyFile(src, dest, 0)\n            return\n        except win32api.error as details:\n            if details.winerror == 5:  # access denied - user not admin.\n                raise", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "LoadSystemModule", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def LoadSystemModule(lib_dir, modname):\n    # See if this is a debug build.\n    import importlib.machinery\n    import importlib.util\n    suffix = \"_d\" if \"_d.pyd\" in importlib.machinery.EXTENSION_SUFFIXES else \"\"\n    filename = \"%s%d%d%s.dll\" % (\n        modname,\n        sys.version_info.major,\n        sys.version_info.minor,\n        suffix,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "SetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def SetPyKeyVal(key_name, value_name, value):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.CreateKey(root_key, key_name)\n        try:\n            winreg.SetValueEx(my_key, value_name, 0, winreg.REG_SZ, value)\n            if verbose:\n                print(f\"-> {root_key_name}\\\\{key_name}[{value_name}]={value!r}\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "UnsetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def UnsetPyKeyVal(key_name, value_name, delete_key=False):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.OpenKey(root_key, key_name, 0, winreg.KEY_SET_VALUE)\n        try:\n            winreg.DeleteValue(my_key, value_name)\n            if verbose:\n                print(f\"-> DELETE {root_key_name}\\\\{key_name}[{value_name}]\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterCOMObjects", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterCOMObjects(register=True):\n    import win32com.server.register\n    if register:\n        func = win32com.server.register.RegisterClasses\n    else:\n        func = win32com.server.register.UnregisterClasses\n    flags = {}\n    if not verbose:\n        flags[\"quiet\"] = 1\n    for module, klass_name in com_modules:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterHelpFile", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterHelpFile(register=True, lib_dir=None):\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    if register:\n        # Register the .chm help file.\n        chm_file = os.path.join(lib_dir, \"PyWin32.chm\")\n        if os.path.isfile(chm_file):\n            # This isn't recursive, so if 'Help' doesn't exist, we croak\n            SetPyKeyVal(\"Help\", None, None)\n            SetPyKeyVal(\"Help\\\\Pythonwin Reference\", None, chm_file)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterPyt<PERSON><PERSON>", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterPythonwin(register=True, lib_dir=None):\n    \"\"\"Add (or remove) Pythonwin to context menu for python scripts.\n    ??? Should probably also add Edit command for pys files also.\n    Also need to remove these keys on uninstall, but there's no function\n    to add registry entries to uninstall log ???\n    \"\"\"\n    import os\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    classes_root = get_root_hkey()", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_shortcuts_folder", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_shortcuts_folder():\n    if get_root_hkey() == winreg.HKEY_LOCAL_MACHINE:\n        try:\n            fldr = get_special_folder_path(\"CSIDL_COMMON_PROGRAMS\")\n        except OSError:\n            # No CSIDL_COMMON_PROGRAMS on this platform\n            fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")\n    else:\n        # non-admin install - always goes in this user's start menu.\n        fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_system_dir", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_system_dir():\n    import win32api  # we assume this exists.\n    try:\n        import pythoncom\n        import win32process\n        from win32com.shell import shell, shellcon\n        try:\n            if win32process.IsWow64Process():\n                return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEMX86)\n            return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEM)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "fixup_dbi", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def fixup_dbi():\n    # We used to have a dbi.pyd with our .pyd files, but now have a .py file.\n    # If the user didn't uninstall, they will find the .pyd which will cause\n    # problems - so handle that.\n    import win32api\n    import win32con\n    pyd_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi.pyd\")\n    pyd_d_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi_d.pyd\")\n    py_name = os.path.join(os.path.dirname(win32con.__file__), \"dbi.py\")\n    for this_pyd in (pyd_name, pyd_d_name):", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "install", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def install(lib_dir):\n    import traceback\n    # The .pth file is now installed as a regular file.\n    # Create the .pth file in the site-packages dir, and use only relative paths\n    # We used to write a .pth directly to sys.prefix - clobber it.\n    if os.path.isfile(os.path.join(sys.prefix, \"pywin32.pth\")):\n        os.unlink(os.path.join(sys.prefix, \"pywin32.pth\"))\n    # The .pth may be new and therefore not loaded in this session.\n    # Setup the paths just in case.\n    for name in \"win32 win32\\\\lib Pythonwin\".split():", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "uninstall", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def uninstall(lib_dir):\n    # First ensure our system modules are loaded from pywin32_system, so\n    # we can remove the ones we copied...\n    LoadSystemModule(lib_dir, \"pywintypes\")\n    LoadSystemModule(lib_dir, \"pythoncom\")\n    try:\n        RegisterCOMObjects(False)\n    except Exception as why:\n        print(f\"Failed to unregister COM objects: {why}\")\n    try:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verify_destination", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def verify_destination(location: str) -> str:\n    location = os.path.abspath(location)\n    if not os.path.isdir(location):\n        raise argparse.ArgumentTypeError(\n            f'Path \"{location}\" is not an existing directory!'\n        )\n    return location\ndef main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,\n        description=\"\"\"A post-install script for the pywin32 extensions.\n    * Typical usage:\n    > python -m pywin32_postinstall -install\n    * or (shorter but you don't have control over which python environment is used)\n    > pywin32_postinstall -install\n    You need to execute this script, with a '-install' parameter,\n    to ensure the environment is setup correctly to install COM objects, services, etc.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "tee_f", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "tee_f = open(\n    os.path.join(\n        tempfile.gettempdir(),  # Send output somewhere so it can be found if necessary...\n        \"pywin32_postinstall.log\",\n    ),\n    \"w\",\n)\nclass Tee:\n    def __init__(self, file):\n        self.f = file", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stderr", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stderr = <PERSON><PERSON>(sys.stderr)\nsys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stdout", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "com_modules", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "com_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0\n# Verbosity of output messages.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "silent", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "silent = 0\n# Verbosity of output messages.\nverbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verbose", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "verbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "root_key_name", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "root_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "run_test", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()\n    result = subprocess.run(cmd, check=False, cwd=dirname)\n    print(f\"*** Test script '{script}' exited with {result.returncode}\")\n    sys.stdout.flush()\n    if result.returncode:", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "find_and_run", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def find_and_run(possible_locations, extras):\n    for maybe in possible_locations:\n        if os.path.isfile(maybe):\n            run_test(maybe, extras)\n            break\n    else:\n        raise RuntimeError(\n            \"Failed to locate a test script in one of %s\" % possible_locations\n        )\ndef main():", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def main():\n    import argparse\n    code_directories = [project_root] + site_packages\n    parser = argparse.ArgumentParser(\n        description=\"A script to trigger tests in all subprojects of PyWin32.\"\n    )\n    parser.add_argument(\n        \"-no-user-interaction\",\n        default=False,\n        action=\"store_true\",", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))\nsite_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "site_packages", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "site_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "failures", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "failures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "get_chat_service", "kind": 2, "importPath": "app.api.v1.chat", "description": "app.api.v1.chat", "peekOfCode": "def get_chat_service(settings: Settings = Depends(get_settings)) -> ConversationService:\n    \"\"\"获取聊天服务实例\"\"\"\n    from app.services.rag.rag_settings import get_rag_config_manager\n    rag_config_manager = get_rag_config_manager()\n    return ConversationService(settings, rag_config_manager)\*************(\"/\", response_model=ChatResponse)\nasync def intelligent_chat(\n    request: ChatRequest,\n    chat_service: ConversationService = Depends(get_chat_service)\n):", "detail": "app.api.v1.chat", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "app.api.v1.chat", "description": "app.api.v1.chat", "peekOfCode": "router = APIRouter(prefix=\"/chat\", tags=[\"智能聊天\"])\ndef get_chat_service(settings: Settings = Depends(get_settings)) -> ConversationService:\n    \"\"\"获取聊天服务实例\"\"\"\n    from app.services.rag.rag_settings import get_rag_config_manager\n    rag_config_manager = get_rag_config_manager()\n    return ConversationService(settings, rag_config_manager)\*************(\"/\", response_model=ChatResponse)\nasync def intelligent_chat(\n    request: ChatRequest,\n    chat_service: ConversationService = Depends(get_chat_service)", "detail": "app.api.v1.chat", "documentation": {}}, {"label": "get_conversation_service", "kind": 2, "importPath": "app.api.v1.conversation", "description": "app.api.v1.conversation", "peekOfCode": "def get_conversation_service(\n    settings: Settings = Depends(get_settings),\n    rag_config_manager: RAGConfigManager = Depends(get_rag_config_manager)\n) -> ConversationService:\n    \"\"\"获取对话服务实例\"\"\"\n    return ConversationService(settings, rag_config_manager)\*************(\"/chat\", response_model=ChatResponse)\nasync def intelligent_chat(\n    request: ChatRequest,\n    conv_service: ConversationService = Depends(get_conversation_service)", "detail": "app.api.v1.conversation", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "app.api.v1.conversation", "description": "app.api.v1.conversation", "peekOfCode": "router = APIRouter(prefix=\"/conversation\", tags=[\"智能对话\"])\ndef get_conversation_service(\n    settings: Settings = Depends(get_settings),\n    rag_config_manager: RAGConfigManager = Depends(get_rag_config_manager)\n) -> ConversationService:\n    \"\"\"获取对话服务实例\"\"\"\n    return ConversationService(settings, rag_config_manager)\*************(\"/chat\", response_model=ChatResponse)\nasync def intelligent_chat(\n    request: ChatRequest,", "detail": "app.api.v1.conversation", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.api.v1.course", "description": "app.api.v1.course", "peekOfCode": "logger = get_logger(\"course_api\")\nrouter = APIRouter(prefix=\"/course\", tags=[\"课程管理\"])\***************(\"/{course_id}\")\nasync def delete_course(course_id: str):\n    \"\"\"\n    删除整个课程及其所有数据\n    删除内容包括：\n    - data/uploads/{course_id} 文件夹\n    - data/outputs/outlines/{course_id} 文件夹\n    - Qdrant中所有course_id匹配的向量点", "detail": "app.api.v1.course", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "app.api.v1.course", "description": "app.api.v1.course", "peekOfCode": "router = APIRouter(prefix=\"/course\", tags=[\"课程管理\"])\***************(\"/{course_id}\")\nasync def delete_course(course_id: str):\n    \"\"\"\n    删除整个课程及其所有数据\n    删除内容包括：\n    - data/uploads/{course_id} 文件夹\n    - data/outputs/outlines/{course_id} 文件夹\n    - Qdrant中所有course_id匹配的向量点\n    Args:", "detail": "app.api.v1.course", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.api.v1.course_materials", "description": "app.api.v1.course_materials", "peekOfCode": "logger = get_logger(\"course_materials_api\")\nrouter = APIRouter(prefix=\"/course-materials\", tags=[\"课程材料处理\"])\*************(\n    \"/process\",\n    response_model=CourseProcessResponse,\n    summary=\"统一处理课程材料\",\n    description=\"上传课程材料并自动完成大纲生成和RAG索引建立\"\n)\nasync def process_course_material(\n    file: UploadFile = File(..., description=\"课程材料文件（支持.md和.txt格式）\"),", "detail": "app.api.v1.course_materials", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "app.api.v1.course_materials", "description": "app.api.v1.course_materials", "peekOfCode": "router = APIRouter(prefix=\"/course-materials\", tags=[\"课程材料处理\"])\*************(\n    \"/process\",\n    response_model=CourseProcessResponse,\n    summary=\"统一处理课程材料\",\n    description=\"上传课程材料并自动完成大纲生成和RAG索引建立\"\n)\nasync def process_course_material(\n    file: UploadFile = File(..., description=\"课程材料文件（支持.md和.txt格式）\"),\n    course_id: str = Form(..., description=\"课程ID\"),", "detail": "app.api.v1.course_materials", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.api.v1.outline", "description": "app.api.v1.outline", "peekOfCode": "logger = get_logger(\"outline_api\")\nrouter = APIRouter(prefix=\"/outline\", tags=[\"大纲生成\"])\n# 存储任务状态的简单内存存储（生产环境应使用数据库）\ntask_storage = {}\*************(\n    \"/generate\",\n    response_model=OutlineGenerateResponse,\n    summary=\"生成文档大纲\",\n    description=\"上传Markdown或文本文档并生成结构化大纲\"\n)", "detail": "app.api.v1.outline", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "app.api.v1.outline", "description": "app.api.v1.outline", "peekOfCode": "router = APIRouter(prefix=\"/outline\", tags=[\"大纲生成\"])\n# 存储任务状态的简单内存存储（生产环境应使用数据库）\ntask_storage = {}\*************(\n    \"/generate\",\n    response_model=OutlineGenerateResponse,\n    summary=\"生成文档大纲\",\n    description=\"上传Markdown或文本文档并生成结构化大纲\"\n)\nasync def generate_outline(", "detail": "app.api.v1.outline", "documentation": {}}, {"label": "task_storage", "kind": 5, "importPath": "app.api.v1.outline", "description": "app.api.v1.outline", "peekOfCode": "task_storage = {}\*************(\n    \"/generate\",\n    response_model=OutlineGenerateResponse,\n    summary=\"生成文档大纲\",\n    description=\"上传Markdown或文本文档并生成结构化大纲\"\n)\nasync def generate_outline(\n    file: UploadFile = File(..., description=\"要处理的Markdown或文本文件(.md/.txt)\"),\n    course_id: str = Form(..., description=\"课程ID\"),", "detail": "app.api.v1.outline", "documentation": {}}, {"label": "get_document_indexing_service", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def get_document_indexing_service(\n    settings: Settings = Depends(get_settings),\n    rag_config_manager: RAGConfigManager = Depends(get_rag_config_manager)\n) -> DocumentIndexingService:\n    \"\"\"获取文档索引服务实例\"\"\"\n    return DocumentIndexingService(settings, rag_config_manager)\*************(\"/index\", response_model=IndexResponse)\nasync def build_index(\n    file: UploadFile = File(...),\n    course_id: str = Form(...),", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "get_collections", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def get_collections(\n    doc_service: DocumentIndexingService = Depends(get_document_indexing_service)\n):\n    \"\"\"\n    获取所有集合列表\n    返回详细的集合信息，包括向量数量等统计信息\n    \"\"\"\n    try:\n        collections = doc_service.get_collections()\n        logger.info(f\"获取集合列表成功: {len(collections)} 个集合\")", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "get_collection_info", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def get_collection_info(\n    collection_name: str,\n    doc_service: DocumentIndexingService = Depends(get_document_indexing_service)\n):\n    \"\"\"\n    获取指定集合的详细信息\n    - **collection_name**: 集合名称\n    \"\"\"\n    try:\n        collection_info = doc_service.get_collection_info(collection_name)", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "delete_collection", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def delete_collection(\n    collection_name: str,\n    doc_service: DocumentIndexingService = Depends(get_document_indexing_service)\n):\n    \"\"\"\n    删除指定集合\n    - **collection_name**: 要删除的集合名称\n    \"\"\"\n    try:\n        success = doc_service.delete_collection(collection_name)", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "delete_documents_by_course", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def delete_documents_by_course(\n    course_id: str,\n    collection_name: Optional[str] = None,\n    doc_service: DocumentIndexingService = Depends(get_document_indexing_service)\n):\n    \"\"\"\n    删除指定课程的所有文档\n    - **course_id**: 课程ID\n    - **collection_name**: 集合名称（可选）\n    \"\"\"", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "delete_documents_by_material", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def delete_documents_by_material(\n    course_id: str,\n    course_material_id: str,\n    collection_name: Optional[str] = None,\n    doc_service: DocumentIndexingService = Depends(get_document_indexing_service)\n):\n    \"\"\"\n    删除指定课程材料的所有文档\n    - **course_id**: 课程ID\n    - **course_material_id**: 课程材料ID", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "count_documents", "kind": 2, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "def count_documents(\n    collection_name: str,\n    doc_service: DocumentIndexingService = Depends(get_document_indexing_service)\n):\n    \"\"\"\n    统计集合中的文档数量\n    - **collection_name**: 集合名称\n    \"\"\"\n    try:\n        count = doc_service.count_documents(collection_name)", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "app.api.v1.rag", "description": "app.api.v1.rag", "peekOfCode": "router = APIRouter(prefix=\"/rag\", tags=[\"RAG\"])\ndef get_document_indexing_service(\n    settings: Settings = Depends(get_settings),\n    rag_config_manager: RAGConfigManager = Depends(get_rag_config_manager)\n) -> DocumentIndexingService:\n    \"\"\"获取文档索引服务实例\"\"\"\n    return DocumentIndexingService(settings, rag_config_manager)\*************(\"/index\", response_model=IndexResponse)\nasync def build_index(\n    file: UploadFile = File(...),", "detail": "app.api.v1.rag", "documentation": {}}, {"label": "ensure_directories", "kind": 2, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "def ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"\n    directories = [\n        DATA_DIR,\n        UPLOADS_DIR,\n        OUTPUTS_DIR,\n        TEMP_DIR,\n        OUTLINES_DIR,\n        RAG_DIR,\n        GRAPHRAG_DIR,", "detail": "app.constants.paths", "documentation": {}}, {"label": "PROJECT_ROOT", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "PROJECT_ROOT = Path(__file__).parent.parent.parent\n# 数据目录\nDATA_DIR = PROJECT_ROOT / \"data\"\nUPLOADS_DIR = DATA_DIR / \"uploads\"\nOUTPUTS_DIR = DATA_DIR / \"outputs\"\nTEMP_DIR = DATA_DIR / \"tmp\"\n# 输出子目录\nOUTLINES_DIR = OUTPUTS_DIR / \"outlines\"\nRAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "DATA_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "DATA_DIR = PROJECT_ROOT / \"data\"\nUPLOADS_DIR = DATA_DIR / \"uploads\"\nOUTPUTS_DIR = DATA_DIR / \"outputs\"\nTEMP_DIR = DATA_DIR / \"tmp\"\n# 输出子目录\nOUTLINES_DIR = OUTPUTS_DIR / \"outlines\"\nRAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "UPLOADS_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "UPLOADS_DIR = DATA_DIR / \"uploads\"\nOUTPUTS_DIR = DATA_DIR / \"outputs\"\nTEMP_DIR = DATA_DIR / \"tmp\"\n# 输出子目录\nOUTLINES_DIR = OUTPUTS_DIR / \"outlines\"\nRAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录", "detail": "app.constants.paths", "documentation": {}}, {"label": "OUTPUTS_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "OUTPUTS_DIR = DATA_DIR / \"outputs\"\nTEMP_DIR = DATA_DIR / \"tmp\"\n# 输出子目录\nOUTLINES_DIR = OUTPUTS_DIR / \"outlines\"\nRAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录\nTESTS_DIR = PROJECT_ROOT / \"tests\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "TEMP_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "TEMP_DIR = DATA_DIR / \"tmp\"\n# 输出子目录\nOUTLINES_DIR = OUTPUTS_DIR / \"outlines\"\nRAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录\nTESTS_DIR = PROJECT_ROOT / \"tests\"\n# 应用目录", "detail": "app.constants.paths", "documentation": {}}, {"label": "OUTLINES_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "OUTLINES_DIR = OUTPUTS_DIR / \"outlines\"\nRAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录\nTESTS_DIR = PROJECT_ROOT / \"tests\"\n# 应用目录\nAPP_DIR = PROJECT_ROOT / \"app\"\n# 文件扩展名常量", "detail": "app.constants.paths", "documentation": {}}, {"label": "RAG_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "RAG_DIR = OUTPUTS_DIR / \"rag\"\nGRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录\nTESTS_DIR = PROJECT_ROOT / \"tests\"\n# 应用目录\nAPP_DIR = PROJECT_ROOT / \"app\"\n# 文件扩展名常量\nMARKDOWN_EXTENSIONS = [\".md\", \".markdown\"]", "detail": "app.constants.paths", "documentation": {}}, {"label": "GRAPHRAG_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "GRAPHRAG_DIR = OUTPUTS_DIR / \"graphrag\"\n# 脚本目录\nSCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录\nTESTS_DIR = PROJECT_ROOT / \"tests\"\n# 应用目录\nAPP_DIR = PROJECT_ROOT / \"app\"\n# 文件扩展名常量\nMARKDOWN_EXTENSIONS = [\".md\", \".markdown\"]\nTEXT_EXTENSIONS = [\".txt\"]", "detail": "app.constants.paths", "documentation": {}}, {"label": "SCRIPTS_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "SCRIPTS_DIR = PROJECT_ROOT / \"scripts\"\n# 测试目录\nTESTS_DIR = PROJECT_ROOT / \"tests\"\n# 应用目录\nAPP_DIR = PROJECT_ROOT / \"app\"\n# 文件扩展名常量\nMARKDOWN_EXTENSIONS = [\".md\", \".markdown\"]\nTEXT_EXTENSIONS = [\".txt\"]\nALLOWED_EXTENSIONS = MARKDOWN_EXTENSIONS + TEXT_EXTENSIONS\n# 文件大小限制 (字节)", "detail": "app.constants.paths", "documentation": {}}, {"label": "TESTS_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "TESTS_DIR = PROJECT_ROOT / \"tests\"\n# 应用目录\nAPP_DIR = PROJECT_ROOT / \"app\"\n# 文件扩展名常量\nMARKDOWN_EXTENSIONS = [\".md\", \".markdown\"]\nTEXT_EXTENSIONS = [\".txt\"]\nALLOWED_EXTENSIONS = MARKDOWN_EXTENSIONS + TEXT_EXTENSIONS\n# 文件大小限制 (字节)\nMAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB\nCHUNK_SIZE = 8192  # 8KB", "detail": "app.constants.paths", "documentation": {}}, {"label": "APP_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "APP_DIR = PROJECT_ROOT / \"app\"\n# 文件扩展名常量\nMARKDOWN_EXTENSIONS = [\".md\", \".markdown\"]\nTEXT_EXTENSIONS = [\".txt\"]\nALLOWED_EXTENSIONS = MARKDOWN_EXTENSIONS + TEXT_EXTENSIONS\n# 文件大小限制 (字节)\nMAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB\nCHUNK_SIZE = 8192  # 8KB\n# API 路径前缀\nAPI_V1_PREFIX = \"/api/v1\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "MARKDOWN_EXTENSIONS", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "MARKDOWN_EXTENSIONS = [\".md\", \".markdown\"]\nTEXT_EXTENSIONS = [\".txt\"]\nALLOWED_EXTENSIONS = MARKDOWN_EXTENSIONS + TEXT_EXTENSIONS\n# 文件大小限制 (字节)\nMAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB\nCHUNK_SIZE = 8192  # 8KB\n# API 路径前缀\nAPI_V1_PREFIX = \"/api/v1\"\n# 提示词相关路径\nPROMPTS_DIR = APP_DIR / \"prompts\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "TEXT_EXTENSIONS", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "TEXT_EXTENSIONS = [\".txt\"]\nALLOWED_EXTENSIONS = MARKDOWN_EXTENSIONS + TEXT_EXTENSIONS\n# 文件大小限制 (字节)\nMAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB\nCHUNK_SIZE = 8192  # 8KB\n# API 路径前缀\nAPI_V1_PREFIX = \"/api/v1\"\n# 提示词相关路径\nPROMPTS_DIR = APP_DIR / \"prompts\"\nOUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "ALLOWED_EXTENSIONS", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "ALLOWED_EXTENSIONS = MARKDOWN_EXTENSIONS + TEXT_EXTENSIONS\n# 文件大小限制 (字节)\nMAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB\nCHUNK_SIZE = 8192  # 8KB\n# API 路径前缀\nAPI_V1_PREFIX = \"/api/v1\"\n# 提示词相关路径\nPROMPTS_DIR = APP_DIR / \"prompts\"\nOUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"\nREFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "MAX_FILE_SIZE", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB\nCHUNK_SIZE = 8192  # 8KB\n# API 路径前缀\nAPI_V1_PREFIX = \"/api/v1\"\n# 提示词相关路径\nPROMPTS_DIR = APP_DIR / \"prompts\"\nOUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"\nREFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"\ndef ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"", "detail": "app.constants.paths", "documentation": {}}, {"label": "CHUNK_SIZE", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "CHUNK_SIZE = 8192  # 8KB\n# API 路径前缀\nAPI_V1_PREFIX = \"/api/v1\"\n# 提示词相关路径\nPROMPTS_DIR = APP_DIR / \"prompts\"\nOUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"\nREFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"\ndef ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"\n    directories = [", "detail": "app.constants.paths", "documentation": {}}, {"label": "API_V1_PREFIX", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "API_V1_PREFIX = \"/api/v1\"\n# 提示词相关路径\nPROMPTS_DIR = APP_DIR / \"prompts\"\nOUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"\nREFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"\ndef ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"\n    directories = [\n        DATA_DIR,\n        UPLOADS_DIR,", "detail": "app.constants.paths", "documentation": {}}, {"label": "PROMPTS_DIR", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "PROMPTS_DIR = APP_DIR / \"prompts\"\nOUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"\nREFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"\ndef ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"\n    directories = [\n        DATA_DIR,\n        UPLOADS_DIR,\n        OUTPUTS_DIR,\n        TEMP_DIR,", "detail": "app.constants.paths", "documentation": {}}, {"label": "OUTLINE_PROMPT_FILE", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "OUTLINE_PROMPT_FILE = PROMPTS_DIR / \"outline_generation.txt\"\nREFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"\ndef ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"\n    directories = [\n        DATA_DIR,\n        UPLOADS_DIR,\n        OUTPUTS_DIR,\n        TEMP_DIR,\n        OUTLINES_DIR,", "detail": "app.constants.paths", "documentation": {}}, {"label": "REFINE_PROMPT_FILE", "kind": 5, "importPath": "app.constants.paths", "description": "app.constants.paths", "peekOfCode": "REFINE_PROMPT_FILE = PROMPTS_DIR / \"outline_refine.txt\"\ndef ensure_directories():\n    \"\"\"确保所有必要的目录存在\"\"\"\n    directories = [\n        DATA_DIR,\n        UPLOADS_DIR,\n        OUTPUTS_DIR,\n        TEMP_DIR,\n        OUTLINES_DIR,\n        RAG_DIR,", "detail": "app.constants.paths", "documentation": {}}, {"label": "Settings", "kind": 6, "importPath": "app.core.config", "description": "app.core.config", "peekOfCode": "class Settings(BaseSettings):\n    \"\"\"应用配置类\"\"\"\n    # OpenAI API 配置\n    api_key: str = Field(..., description=\"OpenAI API密钥\")\n    base_url: str = Field(default=\"https://api.openai.com/v1\", description=\"OpenAI API基础URL\")\n    outline_model: str = Field(default=\"gpt-4o-mini\", description=\"大纲生成模型\")\n    refine_model: str = Field(default=\"gpt-4o-mini\", description=\"大纲精简模型\")\n    # RAG 配置\n    rag_embed_model: str = Field(default=\"text-embedding-3-small\", description=\"RAG嵌入模型\")\n    rag_llm_model: str = Field(default=\"gpt-4o-mini\", description=\"RAG语言模型\")", "detail": "app.core.config", "documentation": {}}, {"label": "get_settings", "kind": 2, "importPath": "app.core.config", "description": "app.core.config", "peekOfCode": "def get_settings() -> Settings:\n    \"\"\"获取配置实例 - 用于依赖注入\"\"\"\n    return settings", "detail": "app.core.config", "documentation": {}}, {"label": "settings", "kind": 5, "importPath": "app.core.config", "description": "app.core.config", "peekOfCode": "settings = Settings()\ndef get_settings() -> Settings:\n    \"\"\"获取配置实例 - 用于依赖注入\"\"\"\n    return settings", "detail": "app.core.config", "documentation": {}}, {"label": "get_current_settings", "kind": 2, "importPath": "app.core.deps", "description": "app.core.deps", "peekOfCode": "def get_current_settings() -> Settings:\n    \"\"\"获取当前配置 - 依赖注入用\"\"\"\n    return get_settings()\nasync def validate_upload_file(\n    file: UploadFile,\n    settings: Settings = Depends(get_current_settings)\n) -> UploadFile:\n    \"\"\"验证上传的文件\"\"\"\n    # 检查文件是否存在\n    if not file:", "detail": "app.core.deps", "documentation": {}}, {"label": "generate_task_id", "kind": 2, "importPath": "app.core.deps", "description": "app.core.deps", "peekOfCode": "def generate_task_id() -> str:\n    \"\"\"生成任务ID\"\"\"\n    return str(uuid.uuid4())\ndef generate_filename(original_filename: str, task_id: str = None) -> str:\n    \"\"\"生成安全的文件名\"\"\"\n    if not task_id:\n        task_id = generate_task_id()\n    # 获取文件扩展名\n    file_ext = Path(original_filename).suffix\n    # 生成时间戳", "detail": "app.core.deps", "documentation": {}}, {"label": "generate_filename", "kind": 2, "importPath": "app.core.deps", "description": "app.core.deps", "peekOfCode": "def generate_filename(original_filename: str, task_id: str = None) -> str:\n    \"\"\"生成安全的文件名\"\"\"\n    if not task_id:\n        task_id = generate_task_id()\n    # 获取文件扩展名\n    file_ext = Path(original_filename).suffix\n    # 生成时间戳\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    # 组合文件名: timestamp_taskid_original.ext\n    safe_original = Path(original_filename).stem[:50]  # 限制原文件名长度", "detail": "app.core.deps", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.core.deps", "description": "app.core.deps", "peekOfCode": "logger = get_logger(\"deps\")\ndef get_current_settings() -> Settings:\n    \"\"\"获取当前配置 - 依赖注入用\"\"\"\n    return get_settings()\nasync def validate_upload_file(\n    file: UploadFile,\n    settings: Settings = Depends(get_current_settings)\n) -> UploadFile:\n    \"\"\"验证上传的文件\"\"\"\n    # 检查文件是否存在", "detail": "app.core.deps", "documentation": {}}, {"label": "json_formatter", "kind": 2, "importPath": "app.core.logging", "description": "app.core.logging", "peekOfCode": "def json_formatter(record: Dict[str, Any]) -> str:\n    \"\"\"JSON格式化器\"\"\"\n    log_entry = {\n        \"time\": record[\"time\"].isoformat(),\n        \"level\": record[\"level\"].name,\n        \"message\": record[\"message\"],\n        \"module\": record[\"name\"],\n        \"function\": record[\"function\"],\n        \"line\": record[\"line\"],\n    }", "detail": "app.core.logging", "documentation": {}}, {"label": "text_formatter", "kind": 2, "importPath": "app.core.logging", "description": "app.core.logging", "peekOfCode": "def text_formatter(record: Dict[str, Any]) -> str:\n    \"\"\"文本格式化器\"\"\"\n    return (\n        \"<green>{time:YYYY-MM-DD HH:mm:ss}</green> | \"\n        \"<level>{level: <8}</level> | \"\n        \"<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | \"\n        \"<level>{message}</level>\\n\"\n    )\ndef setup_logging():\n    \"\"\"设置日志配置\"\"\"", "detail": "app.core.logging", "documentation": {}}, {"label": "setup_logging", "kind": 2, "importPath": "app.core.logging", "description": "app.core.logging", "peekOfCode": "def setup_logging():\n    \"\"\"设置日志配置\"\"\"\n    settings = get_settings()\n    # 移除默认的处理器\n    logger.remove()\n    # 选择格式化器 - 暂时使用文本格式避免 JSON 格式化问题\n    formatter = text_formatter\n    # 添加控制台处理器\n    logger.add(\n        sys.stdout,", "detail": "app.core.logging", "documentation": {}}, {"label": "get_logger", "kind": 2, "importPath": "app.core.logging", "description": "app.core.logging", "peekOfCode": "def get_logger(name: str = None):\n    \"\"\"获取日志记录器\"\"\"\n    if name:\n        return logger.bind(name=name)\n    return logger\n# 创建应用日志记录器\napp_logger = get_logger(\"app\")", "detail": "app.core.logging", "documentation": {}}, {"label": "app_logger", "kind": 5, "importPath": "app.core.logging", "description": "app.core.logging", "peekOfCode": "app_logger = get_logger(\"app\")", "detail": "app.core.logging", "documentation": {}}, {"label": "QdrantRepository", "kind": 6, "importPath": "app.repositories.rag_repository", "description": "app.repositories.rag_repository", "peekOfCode": "class QdrantRepository:\n    \"\"\"Qdrant向量数据库仓库类\"\"\"\n    def __init__(self, settings: Settings):\n        \"\"\"初始化Qdrant客户端\"\"\"\n        self.settings = settings\n        self.client = None\n        self._initialize_client()\n    def _initialize_client(self):\n        \"\"\"初始化Qdrant客户端\"\"\"\n        try:", "detail": "app.repositories.rag_repository", "documentation": {}}, {"label": "rag_repository", "kind": 5, "importPath": "app.repositories.rag_repository", "description": "app.repositories.rag_repository", "peekOfCode": "rag_repository = QdrantRepository(get_settings())", "detail": "app.repositories.rag_repository", "documentation": {}}, {"label": "ProcessingStatus", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class ProcessingStatus(str, Enum):\n    \"\"\"处理状态枚举\"\"\"\n    UPLOADING = \"uploading\"                    # 文件上传中\n    OUTLINE_GENERATING = \"outline_generating\"  # 大纲生成中\n    RAG_INDEXING = \"rag_indexing\"             # RAG索引建立中\n    COMPLETED = \"completed\"                    # 处理完成\n    FAILED = \"failed\"                         # 处理失败\nclass ProcessingStep(BaseModel):\n    \"\"\"处理步骤模型\"\"\"\n    step_name: str = Field(..., description=\"步骤名称\")", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "ProcessingStep", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class ProcessingStep(BaseModel):\n    \"\"\"处理步骤模型\"\"\"\n    step_name: str = Field(..., description=\"步骤名称\")\n    status: ProcessingStatus = Field(..., description=\"步骤状态\")\n    message: str = Field(..., description=\"步骤消息\")\n    start_time: Optional[datetime] = Field(None, description=\"开始时间\")\n    end_time: Optional[datetime] = Field(None, description=\"结束时间\")\n    error_message: Optional[str] = Field(None, description=\"错误信息\")\nclass CourseProcessRequest(BaseModel):\n    \"\"\"课程材料处理请求模型\"\"\"", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "CourseProcessRequest", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class CourseProcessRequest(BaseModel):\n    \"\"\"课程材料处理请求模型\"\"\"\n    # 必需参数\n    course_id: str = Field(\n        ...,\n        description=\"课程ID，用于组织文件存储目录\",\n        min_length=1,\n        max_length=50\n    )\n    course_material_id: str = Field(", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "CourseProcessResponse", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class CourseProcessResponse(BaseModel):\n    \"\"\"课程材料处理响应模型\"\"\"\n    # 任务标识信息\n    task_id: str = Field(..., description=\"任务ID\")\n    status: ProcessingStatus = Field(..., description=\"当前处理状态\")\n    message: str = Field(..., description=\"响应消息\")\n    # 进度信息\n    current_step: str = Field(..., description=\"当前处理步骤\")\n    completed_steps: int = Field(default=0, description=\"已完成步骤数\")\n    total_steps: int = Field(default=3, description=\"总步骤数\")", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "TaskStatusQuery", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class TaskStatusQuery(BaseModel):\n    \"\"\"任务状态查询响应模型\"\"\"\n    task_id: str = Field(..., description=\"任务ID\")\n    status: ProcessingStatus = Field(..., description=\"当前处理状态\")\n    message: str = Field(..., description=\"状态消息\")\n    # 进度信息\n    current_step: str = Field(..., description=\"当前处理步骤\")\n    completed_steps: int = Field(..., description=\"已完成步骤数\")\n    total_steps: int = Field(..., description=\"总步骤数\")\n    progress_percentage: float = Field(..., description=\"进度百分比\")", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "CleanupOperation", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class CleanupOperation(BaseModel):\n    \"\"\"清理操作模型\"\"\"\n    operation_type: str = Field(..., description=\"操作类型\")\n    target: str = Field(..., description=\"清理目标\")\n    success: bool = Field(..., description=\"是否成功\")\n    message: str = Field(..., description=\"操作消息\")\n    details: Optional[str] = Field(None, description=\"详细信息\")\nclass CleanupRequest(BaseModel):\n    \"\"\"清理请求模型\"\"\"\n    course_id: str = Field(..., description=\"课程ID\")", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "CleanupRequest", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class CleanupRequest(BaseModel):\n    \"\"\"清理请求模型\"\"\"\n    course_id: str = Field(..., description=\"课程ID\")\n    course_material_id: Optional[str] = Field(None, description=\"课程材料ID，如果不提供则清理整个课程\")\n    cleanup_files: bool = Field(default=True, description=\"是否清理文件系统\")\n    cleanup_rag_data: bool = Field(default=True, description=\"是否清理RAG数据\")\n    cleanup_task_data: bool = Field(default=True, description=\"是否清理任务数据\")\nclass CleanupResponse(BaseModel):\n    \"\"\"清理响应模型\"\"\"\n    success: bool = Field(..., description=\"清理是否成功\")", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "CleanupResponse", "kind": 6, "importPath": "app.schemas.course_materials", "description": "app.schemas.course_materials", "peekOfCode": "class CleanupResponse(BaseModel):\n    \"\"\"清理响应模型\"\"\"\n    success: bool = Field(..., description=\"清理是否成功\")\n    message: str = Field(..., description=\"清理消息\")\n    # 清理范围\n    course_id: str = Field(..., description=\"课程ID\")\n    course_material_id: Optional[str] = Field(None, description=\"课程材料ID\")\n    # 清理操作列表\n    operations: List[CleanupOperation] = Field(default_factory=list, description=\"清理操作列表\")\n    # 清理统计", "detail": "app.schemas.course_materials", "documentation": {}}, {"label": "TaskStatus", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class TaskStatus(str, Enum):\n    \"\"\"任务状态枚举\"\"\"\n    PENDING = \"pending\"      # 等待处理\n    PROCESSING = \"processing\"  # 处理中\n    COMPLETED = \"completed\"   # 已完成\n    FAILED = \"failed\"        # 失败\nclass OutlineGenerateRequest(BaseModel):\n    \"\"\"大纲生成请求模型\"\"\"\n    # 注意：文件上传通过 FastAPI 的 UploadFile 处理，不在这里定义\n    # 必需参数", "detail": "app.schemas.outline", "documentation": {}}, {"label": "OutlineGenerateRequest", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class OutlineGenerateRequest(BaseModel):\n    \"\"\"大纲生成请求模型\"\"\"\n    # 注意：文件上传通过 FastAPI 的 UploadFile 处理，不在这里定义\n    # 必需参数\n    course_id: str = Field(\n        ...,\n        description=\"课程ID，用于组织文件存储目录\",\n        min_length=1,\n        max_length=50\n    )", "detail": "app.schemas.outline", "documentation": {}}, {"label": "OutlineGenerateResponse", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class OutlineGenerateResponse(BaseModel):\n    \"\"\"大纲生成响应模型\"\"\"\n    task_id: str = Field(..., description=\"任务ID\")\n    status: TaskStatus = Field(..., description=\"任务状态\")\n    message: str = Field(..., description=\"响应消息\")\n    # 课程信息\n    course_id: Optional[str] = Field(None, description=\"课程ID\")\n    course_material_id: Optional[str] = Field(None, description=\"课程材料ID\")\n    material_name: Optional[str] = Field(None, description=\"材料名称\")\n    # 成功时返回的数据", "detail": "app.schemas.outline", "documentation": {}}, {"label": "OutlineTaskQuery", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class OutlineTaskQuery(BaseModel):\n    \"\"\"大纲任务查询响应模型\"\"\"\n    task_id: str = Field(..., description=\"任务ID\")\n    status: TaskStatus = Field(..., description=\"任务状态\")\n    message: str = Field(..., description=\"状态消息\")\n    # 课程信息\n    course_id: Optional[str] = Field(None, description=\"课程ID\")\n    course_material_id: Optional[str] = Field(None, description=\"课程材料ID\")\n    material_name: Optional[str] = Field(None, description=\"材料名称\")\n    # 任务详情", "detail": "app.schemas.outline", "documentation": {}}, {"label": "OutlineFileResponse", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class OutlineFileResponse(BaseModel):\n    \"\"\"获取outline文件响应模型\"\"\"\n    success: bool = Field(..., description=\"请求是否成功\")\n    message: str = Field(..., description=\"响应消息\")\n    # 课程信息\n    course_id: str = Field(..., description=\"课程ID\")\n    course_material_id: str = Field(..., description=\"课程材料ID\")\n    material_name: Optional[str] = Field(None, description=\"材料名称\")\n    # 文件信息\n    file_path: str = Field(..., description=\"文件路径\")", "detail": "app.schemas.outline", "documentation": {}}, {"label": "ErrorResponse", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class ErrorResponse(BaseModel):\n    \"\"\"错误响应模型\"\"\"\n    error: str = Field(..., description=\"错误类型\")\n    message: str = Field(..., description=\"错误消息\")\n    detail: Optional[str] = Field(None, description=\"详细错误信息\")\n    task_id: Optional[str] = Field(None, description=\"相关任务ID\")\n    timestamp: datetime = Field(default_factory=datetime.now, description=\"错误时间\")\n    class Config:\n        json_schema_extra = {\n            \"example\": {", "detail": "app.schemas.outline", "documentation": {}}, {"label": "HealthResponse", "kind": 6, "importPath": "app.schemas.outline", "description": "app.schemas.outline", "peekOfCode": "class HealthResponse(BaseModel):\n    \"\"\"健康检查响应模型\"\"\"\n    status: str = Field(..., description=\"服务状态\")\n    timestamp: datetime = Field(default_factory=datetime.now, description=\"检查时间\")\n    version: str = Field(default=\"0.1.0\", description=\"应用版本\")\n    # 服务信息\n    uptime: Optional[float] = Field(None, description=\"运行时间(秒)\")\n    # 依赖服务状态\n    openai_api: Optional[str] = Field(None, description=\"OpenAI API状态\")\n    class Config:", "detail": "app.schemas.outline", "documentation": {}}, {"label": "ChatMode", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class ChatMode(str, Enum):\n    \"\"\"聊天模式枚举\"\"\"\n    QUERY = \"query\"  # 检索模式（condense_question ChatEngine）\n    CHAT = \"chat\"    # 直接聊天模式（simple ChatEngine）\nclass ChatEngineType(str, Enum):\n    \"\"\"聊天引擎类型枚举\"\"\"\n    CONDENSE_PLUS_CONTEXT = \"condense_plus_context\"  # 检索增强模式\n    SIMPLE = \"simple\"  # 简单对话模式\nclass DocumentMetadata(BaseModel):\n    \"\"\"文档元数据模型\"\"\"", "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatEngineType", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class ChatEngineType(str, Enum):\n    \"\"\"聊天引擎类型枚举\"\"\"\n    CONDENSE_PLUS_CONTEXT = \"condense_plus_context\"  # 检索增强模式\n    SIMPLE = \"simple\"  # 简单对话模式\nclass DocumentMetadata(BaseModel):\n    \"\"\"文档元数据模型\"\"\"\n    course_id: str = Field(..., description=\"课程ID\")\n    course_material_id: str = Field(..., description=\"课程材料ID\")\n    course_material_name: str = Field(..., description=\"课程材料名称\")\n    file_path: Optional[str] = Field(None, description=\"文件路径\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "DocumentMetadata", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class DocumentMetadata(BaseModel):\n    \"\"\"文档元数据模型\"\"\"\n    course_id: str = Field(..., description=\"课程ID\")\n    course_material_id: str = Field(..., description=\"课程材料ID\")\n    course_material_name: str = Field(..., description=\"课程材料名称\")\n    file_path: Optional[str] = Field(None, description=\"文件路径\")\n    file_size: Optional[int] = Field(None, description=\"文件大小\")\n    upload_time: Optional[str] = Field(None, description=\"上传时间\")\nclass IndexRequest(BaseModel):\n    \"\"\"索引建立请求模型\"\"\"", "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexRequest", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class IndexRequest(BaseModel):\n    \"\"\"索引建立请求模型\"\"\"\n    file_content: str = Field(..., description=\"文件内容\")\n    metadata: DocumentMetadata = Field(..., description=\"文档元数据\")\n    collection_name: Optional[str] = Field(None, description=\"集合名称，默认使用配置中的名称\")\nclass ChatMessage(BaseModel):\n    \"\"\"聊天消息模型\"\"\"\n    role: str = Field(..., description=\"角色：user/assistant\")\n    content: str = Field(..., description=\"消息内容\")\nclass ChatMemory(BaseModel):", "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatMessage", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class ChatMessage(BaseModel):\n    \"\"\"聊天消息模型\"\"\"\n    role: str = Field(..., description=\"角色：user/assistant\")\n    content: str = Field(..., description=\"消息内容\")\nclass ChatMemory(BaseModel):\n    \"\"\"聊天记忆模型\"\"\"\n    messages: List[ChatMessage] = Field(default_factory=list, description=\"聊天消息列表\")\n    summary: Optional[str] = Field(None, description=\"对话摘要\")\n    token_count: int = Field(default=0, description=\"Token数量\")\nclass QueryRequest(BaseModel):", "detail": "app.schemas.rag", "documentation": {}}, {"label": "Chat<PERSON><PERSON>ory", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class ChatMemory(BaseModel):\n    \"\"\"聊天记忆模型\"\"\"\n    messages: List[ChatMessage] = Field(default_factory=list, description=\"聊天消息列表\")\n    summary: Optional[str] = Field(None, description=\"对话摘要\")\n    token_count: int = Field(default=0, description=\"Token数量\")\nclass QueryRequest(BaseModel):\n    \"\"\"问答查询请求模型\"\"\"\n    question: str = Field(..., description=\"用户问题\")\n    mode: ChatMode = Field(default=ChatMode.QUERY, description=\"聊天模式\")\n    course_id: Optional[str] = Field(None, description=\"课程ID，用于过滤检索范围\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "QueryRequest", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class QueryRequest(BaseModel):\n    \"\"\"问答查询请求模型\"\"\"\n    question: str = Field(..., description=\"用户问题\")\n    mode: ChatMode = Field(default=ChatMode.QUERY, description=\"聊天模式\")\n    course_id: Optional[str] = Field(None, description=\"课程ID，用于过滤检索范围\")\n    chat_memory: Optional[ChatMemory] = Field(None, description=\"聊天记忆\")\n    collection_name: Optional[str] = Field(None, description=\"集合名称，默认使用配置中的名称\")\n    top_k: Optional[int] = Field(None, description=\"检索Top-K数量，默认使用配置中的值\")\nclass SourceInfo(BaseModel):\n    \"\"\"来源信息模型\"\"\"", "detail": "app.schemas.rag", "documentation": {}}, {"label": "SourceInfo", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class SourceInfo(BaseModel):\n    \"\"\"来源信息模型\"\"\"\n    course_id: str = Field(..., description=\"课程ID\")\n    course_material_id: str = Field(..., description=\"课程材料ID\")\n    course_material_name: str = Field(..., description=\"课程材料名称\")\n    chunk_text: str = Field(..., description=\"相关文本片段\")\n    score: float = Field(..., description=\"相似度分数\")\nclass QueryResponse(BaseModel):\n    \"\"\"问答响应模型\"\"\"\n    answer: str = Field(..., description=\"回答内容\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "QueryResponse", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class QueryResponse(BaseModel):\n    \"\"\"问答响应模型\"\"\"\n    answer: str = Field(..., description=\"回答内容\")\n    sources: List[SourceInfo] = Field(default_factory=list, description=\"来源信息列表\")\n    chat_memory: Optional[ChatMemory] = Field(None, description=\"更新后的聊天记忆\")\n    mode: ChatMode = Field(..., description=\"使用的聊天模式\")\n    processing_time: float = Field(..., description=\"处理时间（秒）\")\nclass IndexResponse(BaseModel):\n    \"\"\"索引建立响应模型\"\"\"\n    success: bool = Field(..., description=\"是否成功\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "IndexResponse", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class IndexResponse(BaseModel):\n    \"\"\"索引建立响应模型\"\"\"\n    success: bool = Field(..., description=\"是否成功\")\n    message: str = Field(..., description=\"响应消息\")\n    document_count: int = Field(default=0, description=\"处理的文档数量\")\n    chunk_count: int = Field(default=0, description=\"生成的文本块数量\")\n    processing_time: float = Field(..., description=\"处理时间（秒）\")\n    collection_name: str = Field(..., description=\"集合名称\")\nclass CollectionInfo(BaseModel):\n    \"\"\"集合信息模型\"\"\"", "detail": "app.schemas.rag", "documentation": {}}, {"label": "CollectionInfo", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class CollectionInfo(BaseModel):\n    \"\"\"集合信息模型\"\"\"\n    name: str = Field(..., description=\"集合名称\")\n    vectors_count: int = Field(..., description=\"向量数量\")\n    indexed_only: bool = Field(..., description=\"是否仅索引\")\n    payload_schema: Dict[str, Any] = Field(default_factory=dict, description=\"载荷模式\")\nclass CollectionListResponse(BaseModel):\n    \"\"\"集合列表响应模型\"\"\"\n    collections: List[CollectionInfo] = Field(..., description=\"集合列表\")\n    total_count: int = Field(..., description=\"总数量\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "CollectionListResponse", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class CollectionListResponse(BaseModel):\n    \"\"\"集合列表响应模型\"\"\"\n    collections: List[CollectionInfo] = Field(..., description=\"集合列表\")\n    total_count: int = Field(..., description=\"总数量\")\nclass DeleteCollectionResponse(BaseModel):\n    \"\"\"删除集合响应模型\"\"\"\n    success: bool = Field(..., description=\"是否成功\")\n    message: str = Field(..., description=\"响应消息\")\n    collection_name: str = Field(..., description=\"被删除的集合名称\")\n# 新增聊天相关模型", "detail": "app.schemas.rag", "documentation": {}}, {"label": "DeleteCollectionResponse", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class DeleteCollectionResponse(BaseModel):\n    \"\"\"删除集合响应模型\"\"\"\n    success: bool = Field(..., description=\"是否成功\")\n    message: str = Field(..., description=\"响应消息\")\n    collection_name: str = Field(..., description=\"被删除的集合名称\")\n# 新增聊天相关模型\nclass ChatRequest(BaseModel):\n    \"\"\"智能聊天请求模型\"\"\"\n    conversation_id: str = Field(..., description=\"对话会话ID，用作Redis存储键\")\n    course_id: Optional[str] = Field(None, description=\"课程ID（与course_material_id二选一）\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatRequest", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class ChatRequest(BaseModel):\n    \"\"\"智能聊天请求模型\"\"\"\n    conversation_id: str = Field(..., description=\"对话会话ID，用作Redis存储键\")\n    course_id: Optional[str] = Field(None, description=\"课程ID（与course_material_id二选一）\")\n    course_material_id: Optional[str] = Field(None, description=\"课程材料ID（与course_id二选一）\")\n    chat_engine_type: ChatEngineType = Field(..., description=\"聊天引擎类型\")\n    question: str = Field(..., description=\"用户问题\")\n    collection_name: Optional[str] = Field(None, description=\"集合名称，默认使用配置中的名称\")\nclass ChatResponse(BaseModel):\n    \"\"\"智能聊天响应模型\"\"\"", "detail": "app.schemas.rag", "documentation": {}}, {"label": "ChatResponse", "kind": 6, "importPath": "app.schemas.rag", "description": "app.schemas.rag", "peekOfCode": "class ChatResponse(BaseModel):\n    \"\"\"智能聊天响应模型\"\"\"\n    answer: str = Field(..., description=\"AI回答内容\")\n    sources: List[SourceInfo] = Field(default_factory=list, description=\"检索到的来源信息（仅condense_plus_context模式）\")\n    conversation_id: str = Field(..., description=\"对话会话ID\")\n    chat_engine_type: ChatEngineType = Field(..., description=\"使用的聊天引擎类型\")\n    filter_info: Optional[str] = Field(None, description=\"使用的过滤条件信息\")\n    processing_time: float = Field(..., description=\"处理时间（秒）\")", "detail": "app.schemas.rag", "documentation": {}}, {"label": "CleanupService", "kind": 6, "importPath": "app.services.course_material.cleanup_service", "description": "app.services.course_material.cleanup_service", "peekOfCode": "class CleanupService:\n    \"\"\"课程材料清理服务\"\"\"\n    def __init__(self):\n        self.settings = get_settings()\n    async def cleanup_course_material(\n        self,\n        request: CleanupRequest\n    ) -> CleanupResponse:\n        \"\"\"\n        清理指定课程材料的所有数据", "detail": "app.services.course_material.cleanup_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.services.course_material.cleanup_service", "description": "app.services.course_material.cleanup_service", "peekOfCode": "logger = get_logger(\"cleanup_service\")\nclass CleanupService:\n    \"\"\"课程材料清理服务\"\"\"\n    def __init__(self):\n        self.settings = get_settings()\n    async def cleanup_course_material(\n        self,\n        request: CleanupRequest\n    ) -> CleanupResponse:\n        \"\"\"", "detail": "app.services.course_material.cleanup_service", "documentation": {}}, {"label": "cleanup_service", "kind": 5, "importPath": "app.services.course_material.cleanup_service", "description": "app.services.course_material.cleanup_service", "peekOfCode": "cleanup_service = CleanupService()", "detail": "app.services.course_material.cleanup_service", "documentation": {}}, {"label": "CourseMaterialProcessService", "kind": 6, "importPath": "app.services.course_material.course_material_process_service", "description": "app.services.course_material.course_material_process_service", "peekOfCode": "class CourseMaterialProcessService:\n    \"\"\"统一课程材料处理服务\"\"\"\n    def __init__(self):\n        self.settings = get_settings()\n        # 存储任务状态的简单内存存储（生产环境应使用数据库）\n        self.task_storage: Dict[str, CourseProcessResponse] = {}\n        # 初始化文档索引服务\n        rag_config_manager = get_rag_config_manager()\n        self.document_indexing_service = DocumentIndexingService(self.settings, rag_config_manager)\n    async def process_course_material(", "detail": "app.services.course_material.course_material_process_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.services.course_material.course_material_process_service", "description": "app.services.course_material.course_material_process_service", "peekOfCode": "logger = get_logger(\"course_material_process_service\")\nclass CourseMaterialProcessService:\n    \"\"\"统一课程材料处理服务\"\"\"\n    def __init__(self):\n        self.settings = get_settings()\n        # 存储任务状态的简单内存存储（生产环境应使用数据库）\n        self.task_storage: Dict[str, CourseProcessResponse] = {}\n        # 初始化文档索引服务\n        rag_config_manager = get_rag_config_manager()\n        self.document_indexing_service = DocumentIndexingService(self.settings, rag_config_manager)", "detail": "app.services.course_material.course_material_process_service", "documentation": {}}, {"label": "course_material_process_service", "kind": 5, "importPath": "app.services.course_material.course_material_process_service", "description": "app.services.course_material.course_material_process_service", "peekOfCode": "course_material_process_service = CourseMaterialProcessService()", "detail": "app.services.course_material.course_material_process_service", "documentation": {}}, {"label": "OutlineService", "kind": 6, "importPath": "app.services.outline.outline_service", "description": "app.services.outline.outline_service", "peekOfCode": "class OutlineService:\n    \"\"\"大纲生成服务类\"\"\"\n    def __init__(self):\n        self.settings = get_settings()\n        self.client = AsyncOpenAI(\n            api_key=self.settings.api_key,\n            base_url=self.settings.base_url\n        )\n        # 加载提示词模板\n        self._outline_prompt_template = None", "detail": "app.services.outline.outline_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.services.outline.outline_service", "description": "app.services.outline.outline_service", "peekOfCode": "logger = get_logger(\"outline_service\")\nclass OutlineService:\n    \"\"\"大纲生成服务类\"\"\"\n    def __init__(self):\n        self.settings = get_settings()\n        self.client = AsyncOpenAI(\n            api_key=self.settings.api_key,\n            base_url=self.settings.base_url\n        )\n        # 加载提示词模板", "detail": "app.services.outline.outline_service", "documentation": {}}, {"label": "outline_service", "kind": 5, "importPath": "app.services.outline.outline_service", "description": "app.services.outline.outline_service", "peekOfCode": "outline_service = OutlineService()", "detail": "app.services.outline.outline_service", "documentation": {}}, {"label": "ConversationMemoryManager", "kind": 6, "importPath": "app.services.rag.conversation_service", "description": "app.services.rag.conversation_service", "peekOfCode": "class ConversationMemoryManager:\n    \"\"\"对话内存管理器\"\"\"\n    def __init__(self, rag_config_manager: RAGConfigManager):\n        \"\"\"\n        初始化对话内存管理器\n        Args:\n            rag_config_manager: RAG配置管理器\n        \"\"\"\n        self.rag_config_manager = rag_config_manager\n        self._load_prompts()", "detail": "app.services.rag.conversation_service", "documentation": {}}, {"label": "ChatEngineFactory", "kind": 6, "importPath": "app.services.rag.conversation_service", "description": "app.services.rag.conversation_service", "peekOfCode": "class ChatEngineFactory:\n    \"\"\"聊天引擎工厂\"\"\"\n    def __init__(self, app_settings: AppSettings, rag_config_manager: RAGConfigManager):\n        \"\"\"\n        初始化聊天引擎工厂\n        Args:\n            app_settings: 应用配置\n            rag_config_manager: RAG配置管理器\n        \"\"\"\n        self.app_settings = app_settings", "detail": "app.services.rag.conversation_service", "documentation": {}}, {"label": "ConversationService", "kind": 6, "importPath": "app.services.rag.conversation_service", "description": "app.services.rag.conversation_service", "peekOfCode": "class ConversationService:\n    \"\"\"对话服务类\"\"\"\n    def __init__(self, app_settings: AppSettings, rag_config_manager: RAGConfigManager):\n        \"\"\"\n        初始化对话服务\n        Args:\n            app_settings: 应用配置\n            rag_config_manager: RAG配置管理器\n        \"\"\"\n        self.app_settings = app_settings", "detail": "app.services.rag.conversation_service", "documentation": {}}, {"label": "DocumentIndexingService", "kind": 6, "importPath": "app.services.rag.document_indexing_service", "description": "app.services.rag.document_indexing_service", "peekOfCode": "class DocumentIndexingService:\n    \"\"\"文档索引服务类\"\"\"\n    def __init__(self, app_settings: AppSettings, rag_config_manager: RAGConfigManager):\n        \"\"\"\n        初始化文档索引服务\n        Args:\n            app_settings: 应用配置\n            rag_config_manager: RAG配置管理器\n        \"\"\"\n        self.app_settings = app_settings", "detail": "app.services.rag.document_indexing_service", "documentation": {}}, {"label": "RAGSettings", "kind": 6, "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "peekOfCode": "class RAGSettings(BaseSettings):\n    \"\"\"RAG专用配置类，支持环境变量覆盖\"\"\"\n    # Redis 配置\n    redis_url: str = Field(default=\"redis://localhost:6379\", description=\"Redis连接URL\")\n    redis_ttl: int = Field(default=3600, description=\"Redis数据TTL（秒）\")\n    # 对话配置\n    conversation_token_limit: int = Field(default=4000, description=\"对话内存Token限制\")\n    conversation_similarity_top_k: int = Field(default=6, description=\"对话检索Top-K\")\n    # LLM 配置\n    llm_model: str = Field(default=\"gpt-4o-mini\", description=\"LLM模型名称\")", "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "RAGConfigManager", "kind": 6, "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "peekOfCode": "class RAGConfigManager:\n    \"\"\"RAG配置管理器 - 单例模式\"\"\"\n    _instance: Optional['RAGConfigManager'] = None\n    _initialized: bool = False\n    def __new__(cls) -> 'RAGConfigManager':\n        if cls._instance is None:\n            cls._instance = super().__new__(cls)\n        return cls._instance\n    def __init__(self):\n        if not self._initialized:", "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "get_rag_config_manager", "kind": 2, "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "peekOfCode": "def get_rag_config_manager() -> RAGConfigManager:\n    \"\"\"获取RAG配置管理器实例\"\"\"\n    return rag_config_manager\ndef initialize_rag_config(app_settings: AppSettings) -> None:\n    \"\"\"初始化RAG配置（应用启动时调用）\"\"\"\n    rag_config_manager.initialize(app_settings)", "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "initialize_rag_config", "kind": 2, "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "peekOfCode": "def initialize_rag_config(app_settings: AppSettings) -> None:\n    \"\"\"初始化RAG配置（应用启动时调用）\"\"\"\n    rag_config_manager.initialize(app_settings)", "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "rag_config_manager", "kind": 5, "importPath": "app.services.rag.rag_settings", "description": "app.services.rag.rag_settings", "peekOfCode": "rag_config_manager = RAGConfigManager()\ndef get_rag_config_manager() -> RAGConfigManager:\n    \"\"\"获取RAG配置管理器实例\"\"\"\n    return rag_config_manager\ndef initialize_rag_config(app_settings: AppSettings) -> None:\n    \"\"\"初始化RAG配置（应用启动时调用）\"\"\"\n    rag_config_manager.initialize(app_settings)", "detail": "app.services.rag.rag_settings", "documentation": {}}, {"label": "FileIOUtils", "kind": 6, "importPath": "app.utils.fileio", "description": "app.utils.fileio", "peekOfCode": "class FileIOUtils:\n    \"\"\"文件IO工具类\"\"\"\n    @staticmethod\n    def is_safe_path(file_path: Path, base_path: Path) -> bool:\n        \"\"\"\n        检查文件路径是否安全，防止目录遍历攻击\n        Args:\n            file_path: 要检查的文件路径\n            base_path: 基础路径\n        Returns:", "detail": "app.utils.fileio", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.utils.fileio", "description": "app.utils.fileio", "peekOfCode": "logger = get_logger(\"fileio\")\nclass FileIOUtils:\n    \"\"\"文件IO工具类\"\"\"\n    @staticmethod\n    def is_safe_path(file_path: Path, base_path: Path) -> bool:\n        \"\"\"\n        检查文件路径是否安全，防止目录遍历攻击\n        Args:\n            file_path: 要检查的文件路径\n            base_path: 基础路径", "detail": "app.utils.fileio", "documentation": {}}, {"label": "file_utils", "kind": 5, "importPath": "app.utils.fileio", "description": "app.utils.fileio", "peekOfCode": "file_utils = FileIOUtils()", "detail": "app.utils.fileio", "documentation": {}}, {"label": "IDGenerator", "kind": 6, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "class IDGenerator:\n    \"\"\"ID生成器类\"\"\"\n    @staticmethod\n    def generate_uuid() -> str:\n        \"\"\"生成UUID\"\"\"\n        return str(uuid.uuid4())\n    @staticmethod\n    def generate_short_id(length: int = 8) -> str:\n        \"\"\"\n        生成短ID", "detail": "app.utils.idgen", "documentation": {}}, {"label": "FilenameGenerator", "kind": 6, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "class FilenameGenerator:\n    \"\"\"文件名生成器类\"\"\"\n    @staticmethod\n    def generate_timestamp_filename(\n        original_filename: str,\n        task_id: Optional[str] = None,\n        prefix: str = \"\",\n        suffix: str = \"\"\n    ) -> str:\n        \"\"\"", "detail": "app.utils.idgen", "documentation": {}}, {"label": "PathGenerator", "kind": 6, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "class PathGenerator:\n    \"\"\"路径生成器类\"\"\"\n    @staticmethod\n    def generate_upload_path(\n        base_dir: Path,\n        filename: str,\n        create_subdirs: bool = True\n    ) -> Path:\n        \"\"\"\n        生成上传文件路径（旧版本，保持兼容性）", "detail": "app.utils.idgen", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "logger = get_logger(\"idgen\")\nclass IDGenerator:\n    \"\"\"ID生成器类\"\"\"\n    @staticmethod\n    def generate_uuid() -> str:\n        \"\"\"生成UUID\"\"\"\n        return str(uuid.uuid4())\n    @staticmethod\n    def generate_short_id(length: int = 8) -> str:\n        \"\"\"", "detail": "app.utils.idgen", "documentation": {}}, {"label": "id_generator", "kind": 5, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "id_generator = IDGenerator()\nfilename_generator = FilenameGenerator()\npath_generator = PathGenerator()", "detail": "app.utils.idgen", "documentation": {}}, {"label": "filename_generator", "kind": 5, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "filename_generator = FilenameGenerator()\npath_generator = PathGenerator()", "detail": "app.utils.idgen", "documentation": {}}, {"label": "path_generator", "kind": 5, "importPath": "app.utils.idgen", "description": "app.utils.idgen", "peekOfCode": "path_generator = PathGenerator()", "detail": "app.utils.idgen", "documentation": {}}, {"label": "Timer", "kind": 6, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "class Timer:\n    \"\"\"计时器类\"\"\"\n    def __init__(self, name: str = \"Timer\"):\n        self.name = name\n        self.start_time: Optional[float] = None\n        self.end_time: Optional[float] = None\n        self.elapsed_time: Optional[float] = None\n    def start(self) -> 'Timer':\n        \"\"\"开始计时\"\"\"\n        self.start_time = time.time()", "detail": "app.utils.timers", "documentation": {}}, {"label": "AsyncTimer", "kind": 6, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "class AsyncTimer:\n    \"\"\"异步计时器类\"\"\"\n    def __init__(self, name: str = \"AsyncTimer\"):\n        self.name = name\n        self.start_time: Optional[float] = None\n        self.end_time: Optional[float] = None\n        self.elapsed_time: Optional[float] = None\n    async def start(self) -> 'AsyncTimer':\n        \"\"\"开始计时\"\"\"\n        self.start_time = time.time()", "detail": "app.utils.timers", "documentation": {}}, {"label": "PerformanceMonitor", "kind": 6, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "class PerformanceMonitor:\n    \"\"\"性能监控器\"\"\"\n    def __init__(self):\n        self.metrics: Dict[str, Dict[str, Any]] = {}\n    def record_timing(self, operation: str, elapsed_time: float, **metadata):\n        \"\"\"记录操作耗时\"\"\"\n        if operation not in self.metrics:\n            self.metrics[operation] = {\n                \"count\": 0,\n                \"total_time\": 0.0,", "detail": "app.utils.timers", "documentation": {}}, {"label": "TimestampUtils", "kind": 6, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "class TimestampUtils:\n    \"\"\"时间戳工具类\"\"\"\n    @staticmethod\n    def get_current_timestamp() -> float:\n        \"\"\"获取当前时间戳\"\"\"\n        return time.time()\n    @staticmethod\n    def get_current_datetime() -> datetime:\n        \"\"\"获取当前日期时间\"\"\"\n        return datetime.now(timezone.utc)", "detail": "app.utils.timers", "documentation": {}}, {"label": "timer_decorator", "kind": 2, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "def timer_decorator(name: Optional[str] = None):\n    \"\"\"计时器装饰器\"\"\"\n    def decorator(func):\n        operation_name = name or f\"{func.__module__}.{func.__name__}\"\n        if asyncio.iscoroutinefunction(func):\n            async def async_wrapper(*args, **kwargs):\n                async with async_timer(operation_name) as timer:\n                    result = await func(*args, **kwargs)\n                    performance_monitor.record_timing(\n                        operation_name,", "detail": "app.utils.timers", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "logger = get_logger(\"timers\")\nclass Timer:\n    \"\"\"计时器类\"\"\"\n    def __init__(self, name: str = \"Timer\"):\n        self.name = name\n        self.start_time: Optional[float] = None\n        self.end_time: Optional[float] = None\n        self.elapsed_time: Optional[float] = None\n    def start(self) -> 'Timer':\n        \"\"\"开始计时\"\"\"", "detail": "app.utils.timers", "documentation": {}}, {"label": "performance_monitor", "kind": 5, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "performance_monitor = PerformanceMonitor()\ntimestamp_utils = TimestampUtils()", "detail": "app.utils.timers", "documentation": {}}, {"label": "timestamp_utils", "kind": 5, "importPath": "app.utils.timers", "description": "app.utils.timers", "peekOfCode": "timestamp_utils = TimestampUtils()", "detail": "app.utils.timers", "documentation": {}}, {"label": "CourseValidation", "kind": 6, "importPath": "app.utils.validation", "description": "app.utils.validation", "peekOfCode": "class CourseValidation:\n    \"\"\"课程相关验证工具类\"\"\"\n    @staticmethod\n    def validate_course_material_id_unique(\n        course_id: str,\n        course_material_id: str,\n        uploads_base_dir: Path = UPLOADS_DIR\n    ) -> bool:\n        \"\"\"\n        验证在指定course_id下course_material_id是否唯一", "detail": "app.utils.validation", "documentation": {}}, {"label": "FileValidation", "kind": 6, "importPath": "app.utils.validation", "description": "app.utils.validation", "peekOfCode": "class FileValidation:\n    \"\"\"文件验证工具类\"\"\"\n    @staticmethod\n    def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:\n        \"\"\"\n        验证文件扩展名\n        Args:\n            filename: 文件名\n            allowed_extensions: 允许的扩展名列表\n        Returns:", "detail": "app.utils.validation", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.utils.validation", "description": "app.utils.validation", "peekOfCode": "logger = get_logger(\"validation\")\nclass CourseValidation:\n    \"\"\"课程相关验证工具类\"\"\"\n    @staticmethod\n    def validate_course_material_id_unique(\n        course_id: str,\n        course_material_id: str,\n        uploads_base_dir: Path = UPLOADS_DIR\n    ) -> bool:\n        \"\"\"", "detail": "app.utils.validation", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "logger = get_logger(\"main\")\n# 应用启动时间\napp_start_time = time.time()\n@asynccontextmanager\nasync def lifespan(app: FastAPI):\n    \"\"\"应用生命周期管理\"\"\"\n    # 启动时执行\n    logger.info(\"🚀 AI Backend 应用启动中...\")\n    # 获取配置\n    settings = get_settings()", "detail": "app.main", "documentation": {}}, {"label": "app_start_time", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "app_start_time = time.time()\n@asynccontextmanager\nasync def lifespan(app: FastAPI):\n    \"\"\"应用生命周期管理\"\"\"\n    # 启动时执行\n    logger.info(\"🚀 AI Backend 应用启动中...\")\n    # 获取配置\n    settings = get_settings()\n    # 确保目录存在\n    try:", "detail": "app.main", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "app = FastAPI(\n    title=\"AI Backend API\",\n    description=__description__,\n    version=__version__,\n    docs_url=\"/docs\",\n    redoc_url=\"/redoc\",\n    openapi_url=\"/openapi.json\",\n    lifespan=lifespan\n)\n# 获取配置", "detail": "app.main", "documentation": {}}, {"label": "settings", "kind": 5, "importPath": "app.main", "description": "app.main", "peekOfCode": "settings = get_settings()\n# 添加 CORS 中间件\napp.add_middleware(\n    CORSMiddleware,\n    allow_origins=[\"*\"],  # 生产环境应该限制具体域名\n    allow_credentials=True,\n    allow_methods=[\"*\"],\n    allow_headers=[\"*\"],\n)\n# 请求日志中间件", "detail": "app.main", "documentation": {}}, {"label": "RAGIndexBuilder", "kind": 6, "importPath": "scripts.build_rag_index", "description": "scripts.build_rag_index", "peekOfCode": "class RAGIndexBuilder:\n    \"\"\"RAG索引构建器\"\"\"\n    def __init__(self):\n        \"\"\"初始化构建器\"\"\"\n        self.settings = get_settings()\n        self.rag_service = RAGService(self.settings)\n        self.stats = {\n            \"total_files\": 0,\n            \"processed_files\": 0,\n            \"failed_files\": 0,", "detail": "scripts.build_rag_index", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "scripts.build_rag_index", "description": "scripts.build_rag_index", "peekOfCode": "project_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\nfrom app.core.config import get_settings\nfrom app.services.rag_service import RAGService\nfrom app.schemas.rag import IndexRequest, DocumentMetadata\nclass RAGIndexBuilder:\n    \"\"\"RAG索引构建器\"\"\"\n    def __init__(self):\n        \"\"\"初始化构建器\"\"\"\n        self.settings = get_settings()", "detail": "scripts.build_rag_index", "documentation": {}}, {"label": "RAGDataManager", "kind": 6, "importPath": "scripts.rag_data_manager", "description": "scripts.rag_data_manager", "peekOfCode": "class RAGDataManager:\n    \"\"\"RAG数据管理器\"\"\"\n    def __init__(self):\n        \"\"\"初始化管理器\"\"\"\n        self.settings = get_settings()\n        self.qdrant_repo = QdrantRepository(self.settings)\n    async def list_collections(self) -> List[Dict[str, Any]]:\n        \"\"\"列出所有集合\"\"\"\n        try:\n            collections = await self.qdrant_repo.get_collections()", "detail": "scripts.rag_data_manager", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "scripts.rag_data_manager", "description": "scripts.rag_data_manager", "peekOfCode": "project_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\nfrom app.core.config import get_settings\nfrom app.repositories.rag_repository import QdrantRepository\nclass RAGDataManager:\n    \"\"\"RAG数据管理器\"\"\"\n    def __init__(self):\n        \"\"\"初始化管理器\"\"\"\n        self.settings = get_settings()\n        self.qdrant_repo = QdrantRepository(self.settings)", "detail": "scripts.rag_data_manager", "documentation": {}}]